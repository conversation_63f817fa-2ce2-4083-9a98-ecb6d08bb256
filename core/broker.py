#!/usr/bin/env python
"""
IBKR API客户端封装器

提供Interactive Brokers API的高级封装，支持异步连接、历史数据获取、
实时价格查询、订单管理和账户信息查询等功能。
包含连接管理、错误处理和数据转换等核心功能。
"""

import asyncio
import logging
import os
import random
import time
from datetime import datetime, timedelta
from typing import Optional

import nest_asyncio
import pandas as pd

# 导入统一异常处理
from core.exceptions import (
    ErrorCategory,
    IBKRConnectionError,
    async_handle_errors,
    handle_errors,
)

# 导入 ib_insync 并处理错误
try:
    from ib_insync import IB, LimitOrder, MarketOrder, Option, Stock, StopOrder, util
    from ib_insync.order import Order

    # 检查算法订单支持
    try:
        # 检查是否支持算法订单策略
        test_order = LimitOrder("BUY", 100, 10.0)
        test_order.algoStrategy = "Twap"  # 测试是否支持算法策略
        ALGO_ORDER_AVAILABLE = True
    except (AttributeError, ImportError):
        ALGO_ORDER_AVAILABLE = False

    # 配置ib_insync日志级别，过滤信息性消息和合约警告
    util.logToConsole(logging.ERROR)  # 只显示ERROR及以上级别，过滤WARNING

    # 特别设置ib_insync.wrapper的日志级别为ERROR，过滤Error 200等信息
    ib_wrapper_logger = logging.getLogger("ib_insync.wrapper")
    ib_wrapper_logger.setLevel(logging.ERROR)

    IB_INSYNC_AVAILABLE = True
except ImportError as e:
    import logging

    logger = logging.getLogger(__name__)
    logger.warning(f"ib_insync 不可用: {e}")
    logger.info("请安装: pip install ib_insync")
    IB_INSYNC_AVAILABLE = False

    # 创建虚拟类以防止导入错误
    class IB:
        pass

    class Stock:
        pass

    class MarketOrder:
        pass

    class LimitOrder:
        pass

    class Contract:
        pass

    class Order:
        pass

    util = None

from config import DEFAULT_CONFIG, IBKRConfig

# 启用嵌套事件循环
nest_asyncio.apply()


class IBKRClientManager:
    """IBKR客户端管理器 - 单例模式，避免重复创建连接"""

    _instance = None
    _clients = {}  # 存储不同用途的客户端实例

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance

    def get_client(self, purpose: str = "default", config=None) -> "IBKRClient":
        """
        获取指定用途的客户端实例

        参数:
            purpose: 客户端用途 ('default', 'trading', 'data', 'options', 'updater', 'monitor')
            config: IBKR配置，如果为None则使用默认配置

        返回:
            IBKRClient实例
        """
        if purpose not in self._clients:
            # 为不同用途分配不同的客户端ID范围，避免冲突
            client_id_base = {
                "default": 1000,
                "trading": 2000,
                "data": 3000,
                "options": 4000,
                "updater": 5000,
                "monitor": 6000,  # 监控进程专用ID范围
            }

            # 创建配置副本并设置专用客户端ID
            if config is None:
                from config import DEFAULT_CONFIG

                config = DEFAULT_CONFIG.ibkr

            # 如果传入的是UnifiedConfig，提取IBKR配置
            if hasattr(config, "ibkr"):
                config = config.ibkr

            # 创建配置副本
            client_config = type(config)(
                host=config.host,
                port=config.port,
                client_id=client_id_base.get(purpose, 1000) + random.randint(1, 99),
                timeout=getattr(config, "timeout", 30),
                paper_trading=config.paper_trading,
                max_position_size=getattr(config, "max_position_size", 10000.0),
                max_daily_trades=getattr(config, "max_daily_trades", 50),
                stop_loss_pct=getattr(config, "stop_loss_pct", 0.05),
                take_profit_pct=getattr(config, "take_profit_pct", 0.10),
            )

            self._clients[purpose] = IBKRClient(client_config)

        return self._clients[purpose]

    def disconnect_all(self, exclude_purposes: list[str] = None):
        """
        断开所有客户端连接

        参数:
            exclude_purposes: 要排除的客户端用途列表，这些连接将被保留
        """
        if exclude_purposes is None:
            exclude_purposes = []

        # 断开非排除的客户端连接
        clients_to_remove = []
        for purpose, client in self._clients.items():
            if purpose not in exclude_purposes:
                if client.connected:
                    client.disconnect()
                clients_to_remove.append(purpose)

        # 从字典中移除已断开的客户端
        for purpose in clients_to_remove:
            del self._clients[purpose]

    def selective_disconnect(self, purposes: list[str]):
        """
        选择性断开指定用途的客户端连接

        参数:
            purposes: 要断开的客户端用途列表
        """
        for purpose in purposes:
            if purpose in self._clients:
                client = self._clients[purpose]
                if client.connected:
                    client.disconnect()
                del self._clients[purpose]

    def get_connected_clients(self) -> dict[str, "IBKRClient"]:
        """获取所有已连接的客户端"""
        return {
            purpose: client
            for purpose, client in self._clients.items()
            if client.connected
        }


class IBKRClient:
    """Interactive Brokers API 客户端封装器"""

    def __init__(self, config: IBKRConfig = None):
        self.config = config or DEFAULT_CONFIG

        # 如果未指定则自动分配客户端ID
        if self.config.client_id is None:
            # 使用时间戳 + 随机数确保唯一性
            self.config.client_id = int(str(int(time.time()))[-3:]) + random.randint(
                1, 99
            )

        self.ib = IB()
        self.connected = False
        self.logger = self._setup_logging()

    def _setup_logging(self) -> logging.Logger:
        """设置日志配置 - 使用现有的根日志器配置"""
        logger = logging.getLogger("IBKRClient")
        logger.setLevel(getattr(logging, self.config.log_level))

        # 如果根日志器已配置处理器则不添加处理器
        # 这可以防止重复的日志文件并尊重主系统的日志配置
        root_logger = logging.getLogger()
        if root_logger.handlers:
            # 使用现有的根日志器配置
            return logger

        # 后备方案：仅在没有根配置时添加处理器
        if not logger.handlers:
            # 使用统一的根目录logs文件夹
            log_dir = "logs"

            # 确保日志目录存在
            os.makedirs(log_dir, exist_ok=True)

            log_file = f"{log_dir}/trading_system.log"

            handler = logging.FileHandler(log_file)
            formatter = logging.Formatter(
                "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)

            # 同时记录到控制台
            console_handler = logging.StreamHandler()
            console_handler.setFormatter(formatter)
            logger.addHandler(console_handler)

        return logger

    @handle_errors(default_return=False, error_category=ErrorCategory.CONFIGURATION)
    def _validate_config(self) -> bool:
        """验证 IBKR 配置参数"""
        # 检查主机
        if not self.config.host or not isinstance(self.config.host, str):
            self.logger.error("无效的主机配置")
            return False

        # 检查端口
        if not isinstance(self.config.port, int) or self.config.port <= 0:
            self.logger.error(f"无效的端口配置: {self.config.port}")
            return False

        # 验证常见的 IBKR 端口
        valid_ports = [
            7496,
            7497,
            4001,
            4002,
        ]  # 实盘, 纸上交易, 网关实盘, 网关纸上交易
        if self.config.port not in valid_ports:
            self.logger.warning(
                f"Unusual port {self.config.port}, expected one of {valid_ports}"
            )

        # 如果指定了超时时间则检查
        if hasattr(self.config, "timeout"):
            if (
                not isinstance(self.config.timeout, (int, float))
                or self.config.timeout <= 0
            ):
                self.logger.error(
                    f"Invalid timeout configuration: {self.config.timeout}"
                )
                return False

        return True

    async def connect(self) -> bool:
        """连接到 TWS/IB Gateway"""
        if not IB_INSYNC_AVAILABLE:
            self.logger.error("ib_insync不可用 - 无法连接到IBKR")
            return False

        # 验证配置
        if not self._validate_config():
            return False

        try:
            self.ib = IB()

            # 强化的客户端ID生成策略 - 避免冲突

            # 使用更大的随机数范围和更复杂的组合算法
            process_id = os.getpid() % 10000  # 扩大到4位进程ID
            timestamp = int(time.time()) % 1000000  # 使用6位时间戳
            base_random = random.randint(100000, 999999)  # 6位随机数

            # 添加微秒级时间戳增加唯一性（用于后续扩展）

            # 组合生成高度唯一的客户端ID
            if self.config.client_id:
                dynamic_client_id = self.config.client_id
            else:
                # 使用更复杂的算法确保唯一性
                dynamic_client_id = (
                    (timestamp % 10000) * 100000  # 时间戳的4位
                    + (process_id % 100) * 1000  # 进程ID的2位
                    + (base_random % 1000)  # 随机数的3位
                )

            # 确保客户端ID在有效范围内 (1-2147483647)
            dynamic_client_id = max(1, min(dynamic_client_id, 2147483647))

            # 强化的连接重试机制 - 处理客户端ID冲突
            max_attempts = 20  # 增加重试次数
            connection_successful = False

            for attempt in range(max_attempts):
                try:
                    # 确保之前的连接完全断开
                    if hasattr(self, "ib") and self.ib and self.ib.isConnected():
                        self.ib.disconnect()
                        await asyncio.sleep(1)  # 等待断开完成

                    self.logger.info(
                        f"🔌 正在连接到 {self.config.host}:{self.config.port}，客户端ID {dynamic_client_id} (第 {attempt + 1}/{max_attempts} 次尝试)"
                    )

                    # 设置更长的连接超时
                    timeout = getattr(self.config, "timeout", 30)
                    await self.ib.connectAsync(
                        host=self.config.host,
                        port=self.config.port,
                        clientId=dynamic_client_id,
                        timeout=timeout,
                    )

                    # 连接成功，保存使用的客户端ID
                    self.config.client_id = dynamic_client_id
                    connection_successful = True
                    break  # 成功，退出重试循环

                except Exception as e:
                    error_str = str(e).lower()
                    is_client_id_conflict = (
                        "already in use" in error_str
                        or "326" in error_str
                        or "客户号码已被使用" in error_str
                        or "clientid" in error_str
                        or "peer closed connection" in error_str
                    )

                    if is_client_id_conflict:
                        # 生成全新的客户端ID，使用更强的随机性
                        new_timestamp = int(time.time() * 1000) % 1000000
                        new_random = random.randint(100000, 999999)
                        new_microsecond = int(time.time() * 1000000) % 1000

                        dynamic_client_id = (
                            (new_timestamp % 10000) * 100000
                            + (process_id % 100) * 1000
                            + (new_random % 1000)
                            + new_microsecond
                        )
                        dynamic_client_id = max(1, min(dynamic_client_id, 2147483647))

                        self.logger.warning(f"⚠️ 检测到客户端ID冲突: {e}")
                        self.logger.info(
                            f"🔄 生成新的客户端ID {dynamic_client_id} (尝试 {attempt + 1}/{max_attempts})"
                        )

                        if attempt < max_attempts - 1:
                            # 递增等待时间，给Gateway更多时间清理连接
                            wait_time = min(3 + attempt * 2, 20)
                            self.logger.info(f"⏳ 等待 {wait_time}秒后重试...")
                            await asyncio.sleep(wait_time)
                            continue
                    else:
                        # 非客户端ID冲突错误，直接抛出
                        self.logger.error(f"❌ 连接失败，非ID错误: {e}")
                        raise e

                    # 如果已达到最大重试次数
                    if attempt == max_attempts - 1:
                        self.logger.error(
                            f"❌ Failed to connect after {max_attempts} attempts. Last error: {e}"
                        )
                        raise e

            if not connection_successful:
                raise Exception(
                    "Failed to establish connection after all retry attempts"
                )
            self.connected = True
            self.logger.info(
                f"✅ 成功连接到IBKR {self.config.host}:{self.config.port}，客户端ID {dynamic_client_id}"
            )

            # 等待连接稳定并验证连接状态
            await asyncio.sleep(2)

            # 验证连接是否真正建立
            if not self.ib.isConnected():
                self.logger.error(
                    "Connection verification failed - not actually connected"
                )
                self.connected = False
                return False

            # 设置事件处理器以过滤零仓位
            self.ib.positionEvent.clear()  # 清除默认处理程序
            self.ib.updatePortfolioEvent.clear()  # 清除默认处理程序
            self.ib.positionEvent += self._on_position_update
            self.ib.updatePortfolioEvent += self._on_portfolio_update

            # 添加错误处理器 - 清除默认处理器并添加自定义处理器
            self.ib.errorEvent.clear()  # 清除默认错误处理器
            self.ib.errorEvent += self._on_error
            self.logger.info("✅ 自定义错误处理器已注册")

            # 设置ib_insync.wrapper的日志级别为ERROR，过滤Error 200等信息
            ib_wrapper_logger = logging.getLogger("ib_insync.wrapper")
            ib_wrapper_logger.setLevel(logging.ERROR)
            self.logger.debug("✅ ib_insync.wrapper log level set to ERROR")

            # 设置市场数据类型，避免冲突
            try:
                if self.config.paper_trading:
                    # 纸上交易使用延迟数据，避免与真实账户冲突
                    self.ib.reqMarketDataType(3)  # 延迟数据
                    self.logger.info("📊 纸上交易使用延迟市场数据")
                else:
                    # 真实交易尝试实时数据
                    try:
                        self.ib.reqMarketDataType(1)  # 实时数据
                        self.logger.info("📊 使用实时市场数据")
                    except Exception as e:
                        self.ib.reqMarketDataType(3)  # 降级到延迟数据
                        self.logger.warning(f"⚠️ 回退到延迟市场数据: {e}")
            except Exception as e:
                self.logger.warning(f"⚠️ 设置市场数据类型失败: {e}")

            # 检查是否为纸上交易并获取账户信息
            try:
                account_summary = self.ib.accountSummary()
                if account_summary:
                    account_type = next(
                        (
                            item.value
                            for item in account_summary
                            if item.tag == "AccountType"
                        ),
                        "Unknown",
                    )
                    self.logger.info(f"账户类型: {account_type}")

                # 获取管理的账户列表
                accounts = self.ib.managedAccounts()
                if accounts:
                    self.logger.info(f"管理账户: {accounts}")
                else:
                    self.logger.warning("未找到管理账户")

            except Exception as e:
                self.logger.warning(f"无法获取账户信息: {e}")

            return True

        except Exception as e:
            self.logger.error(f"无法连接到IBKR: {e}")
            self.connected = False

            # 清理连接状态
            try:
                if hasattr(self, "ib") and self.ib and self.ib.isConnected():
                    self.ib.disconnect()
            except Exception as cleanup_error:
                self.logger.warning(f"连接清理时出错: {cleanup_error}")

            return False

    def _on_position_update(self, position):
        """处理仓位更新 - 只记录非零仓位"""
        if position.position != 0:
            self.logger.info(
                f"Position update: {position.contract.symbol} = {position.position} shares"
            )

    def _on_portfolio_update(self, portfolio_item):
        """处理投资组合更新 - 只记录非零仓位"""
        if portfolio_item.position != 0:
            self.logger.info(
                f"投资组合更新: {portfolio_item.contract.symbol} = {portfolio_item.position} 股份, "
                f"价值=${portfolio_item.marketValue:.2f}"
            )

    def _on_error(self, reqId, errorCode, errorString, contract=None):
        """处理IBKR错误事件"""
        self.logger.debug(f"🔍 错误处理器被调用: {errorCode} - {errorString}")

        if errorCode == 10197:
            # 市场数据冲突 - 自动切换到延迟数据
            self.logger.warning(f"⚠️ 市场数据冲突 (错误 10197): {errorString}")
            try:
                self.ib.reqMarketDataType(3)  # 切换到延迟数据
                self.logger.info("🔄 已切换到延迟市场数据以解决冲突")
            except Exception as e:
                self.logger.error(f"❌ 切换市场数据类型失败: {e}")
        elif errorCode == 200:
            # 合约未找到 - 正常业务逻辑，使用DEBUG级别
            self.logger.debug(f"📋 合约未找到 (错误 200): {errorString}")
        elif errorCode in [2103, 2104, 2105, 2106, 2107, 2108, 2119, 2157, 2158]:
            # 市场数据农场连接状态 - 信息性消息，使用DEBUG级别
            # 2103: 市场数据场连接中断
            # 2104: 市场数据农场连接状态
            # 2105: 历史市场数据场连接中断
            # 2106: 市场数据农场连接状态
            # 2107: 历史数据农场非活跃状态
            # 2108: 市场数据农场非活跃但可用
            # 2119: 正在连接市场数据农场
            # 2157: Sec-def数据场连接中断
            # 2158: 市场数据农场连接状态
            self.logger.debug(f"📊 市场数据农场状态 (信息 {errorCode}): {errorString}")
        elif errorCode == 201:
            # 订单被拒绝 - 大订单处理相关
            self.logger.error(f"❌ 订单被拒绝 (错误 201): {errorString}")
            if "16500" in errorString or "算法委托单" in errorString:
                self.logger.info(
                    "💡 建议：这可能是大订单超过IBKR限制，系统会自动启用大订单处理逻辑"
                )
        elif errorCode in [326, 327]:
            # 客户端ID冲突 - 已在连接逻辑中处理
            self.logger.error(f"❌ 客户端ID冲突 (错误 {errorCode}): {errorString}")
        elif errorCode in [1100, 1101, 1102]:
            # 连接状态变化 - 重要信息
            self.logger.warning(f"🔌 连接状态 (信息 {errorCode}): {errorString}")
        # 其他错误
        elif errorCode >= 1000:
            # 系统错误
            self.logger.error(f"❌ IBKR系统错误 {errorCode}: {errorString}")
        else:
            # 一般错误
            self.logger.warning(f"⚠️ IBKR警告 {errorCode}: {errorString}")

    def disconnect(self):
        """从TWS/IB Gateway断开连接 - 强化版本确保完全清理"""
        if hasattr(self, "ib") and self.ib:
            try:
                if self.ib.isConnected():
                    self.logger.info("🔌 正在断开与IBKR的连接...")
                    self.ib.disconnect()
                    # 给一些时间让连接完全清理
                    import time

                    time.sleep(1)
                self.connected = False
                self.logger.info("✅ 成功从 IBKR 断开连接")
            except Exception as e:
                self.logger.warning(f"⚠️ 断开连接时出错: {e}")
                self.connected = False
        else:
            self.connected = False

    def create_stock_contract(self, symbol: str, exchange: str = "SMART") -> Stock:
        """创建股票合约"""
        return Stock(symbol, exchange, "USD")

    @async_handle_errors(default_return=None, error_category=ErrorCategory.DATA)
    async def validate_option_contract(self, contract):
        """验证期权合约是否有效"""
        if not self.connected:
            self.logger.error("❌ 未连接到IBKR")
            return None

        qualified = await self.ib.qualifyContractsAsync(contract)
        if qualified:
            self.logger.debug(f"✅ 有效的期权合约: {qualified[0]}")
            return qualified[0]
        self.logger.warning(f"❌ 无效的期权合约: {contract}")
        return None

    def create_option_contract(
        self,
        symbol: str,
        expiry: str,
        strike: float,
        right: str,
        exchange: str = "SMART",
    ):
        """创建期权合约"""
        from ib_insync import Option

        # 对于期权，尝试使用SMART交易所，如果失败则尝试其他交易所
        return Option(symbol, expiry, strike, right, exchange, "USD", multiplier="100")

    @async_handle_errors(default_return=None, error_category=ErrorCategory.DATA)
    async def get_historical_data(
        self,
        symbol: str,
        duration: str = "1 Y",
        bar_size: str = "1 day",
        what_to_show: str = "ADJUSTED_LAST",
    ) -> Optional[pd.DataFrame]:
        """
        Get historical data for a symbol

        参数:
        -----------
        symbol: str
            股票
        duration: str
            持续时间字符串 (例如: '1 Y', '6 M', '30 D')
        bar_size: str
            K线大小 (例如: '1 day', '1 hour', '5 mins')
        what_to_show: str
            显示什么数据 (例如: 'ADJUSTED_LAST', 'TRADES', 'MIDPOINT')
        """
        contract = self.create_stock_contract(symbol)

        # 验证合约
        await self.ib.qualifyContractsAsync(contract)

        # 请求历史数据
        bars = await self.ib.reqHistoricalDataAsync(
            contract,
            endDateTime="",
            durationStr=duration,
            barSizeSetting=bar_size,
            whatToShow=what_to_show,
            useRTH=True,
            formatDate=1,
        )

        if not bars:
            self.logger.warning(f"{symbol} 未接收到历史数据")
            return None

        # 转换为DataFrame
        df = util.df(bars)
        df.set_index("date", inplace=True)
        df.index = pd.to_datetime(df.index)

        self.logger.info(f"为 {symbol} 获取了 {len(df)} 个K线")
        return df

    @async_handle_errors(default_return=None, error_category=ErrorCategory.TRADING)
    async def place_market_order(
        self, symbol: str, quantity: int, action: str = "BUY"
    ) -> Optional[Order]:
        """
        Place a market order with automatic large order handling

        参数:
        -----------
        symbol: str
            股票代码
        quantity: int
            股票数量
        action: str
            订单方向 'BUY' or 'SELL'
        """
        if not self.connected:
            raise IBKRConnectionError("未连接到 IBKR")

        contract = self.create_stock_contract(symbol)
        await self.ib.qualifyContractsAsync(contract)

        # 检查是否为大订单
        if self._check_large_order(quantity):
            self.logger.warning(
                f"⚠️ 检测到大订单市价单: {quantity} 股 (超过 {self.config.max_order_size} 股限制)"
            )

            # 优先使用算法订单（市价TWAP）
            if self.config.enable_algo_orders:
                try:
                    order = self._create_market_algo_order(action, quantity)
                    trade = self.ib.placeOrder(contract, order)

                    self.logger.info(
                        f"✅ 已提交市价TWAP算法订单: {action} {quantity} 股 {symbol}"
                    )
                    return trade
                except Exception as e:
                    self.logger.warning(f"市价算法订单提交失败: {e}，尝试订单分拆")

            # 如果算法订单不可用或失败，使用订单分拆
            if self.config.enable_order_splitting:
                return await self._place_split_market_orders(
                    contract, quantity, action, symbol
                )
            self.logger.error(f"大订单处理被禁用，无法处理 {quantity} 股市价订单")
            raise ValueError(
                f"订单数量 {quantity} 超过限制 {self.config.max_order_size}，且大订单处理被禁用"
            )

        # 常规市价订单处理
        order = MarketOrder(action, quantity)
        trade = self.ib.placeOrder(contract, order)

        self.logger.info(f"✅ 已提交市价订单: {action} {quantity} 股 {symbol}")
        return trade

    def _create_market_algo_order(self, action: str, quantity: int) -> Order:
        """创建市价算法订单（TWAP）"""
        try:
            # 创建市价TWAP算法订单
            order = MarketOrder(action, quantity)
            order.algoStrategy = "Twap"
            order.algoParams = [
                ("startTime", ""),
                ("endTime", ""),
                ("allowPastEndTime", True),
            ]
            order.tif = "DAY"

            self.logger.info(f"🤖 创建市价TWAP算法订单: {action} {quantity} 股")
            return order
        except Exception as e:
            self.logger.warning(f"创建市价算法订单失败，回退到普通市价订单: {e}")
            return MarketOrder(action, quantity)

    async def _place_split_market_orders(
        self, contract, total_quantity: int, action: str, symbol: str
    ) -> Optional[Order]:
        """执行市价订单分拆逻辑"""
        order_sizes = self._split_large_order(total_quantity)
        trades = []

        for i, order_size in enumerate(order_sizes):
            try:
                if i > 0:
                    await asyncio.sleep(self.config.order_split_delay)

                order = MarketOrder(action, order_size)
                trade = self.ib.placeOrder(contract, order)
                trades.append(trade)

                self.logger.info(
                    f"✅ 分拆市价订单 {i + 1}/{len(order_sizes)}: {action} {order_size} 股 {symbol}"
                )

            except Exception as e:
                self.logger.error(f"分拆市价订单 {i + 1} 提交失败: {e}")
                continue

        if trades:
            self.logger.info(
                f"🎯 大市价订单分拆完成: {len(trades)}/{len(order_sizes)} 个订单成功提交"
            )
            return trades[0]
        self.logger.error("所有分拆市价订单都提交失败")
        return None

    @async_handle_errors(default_return=None, error_category=ErrorCategory.TRADING)
    async def place_limit_order(
        self, symbol: str, quantity: int, limit_price: float, action: str = "BUY"
    ) -> Optional[Order]:
        """
        Place a limit order with automatic large order handling

        参数:
        -----------
        symbol: str
            股票代码
        quantity: int
            股票数量
        limit_price: float
            限价
        action: str
            订单方向 'BUY' or 'SELL'
        """
        # 检查实际连接状态而不是依赖self.connected标志
        is_actually_connected = (
            hasattr(self, "ib") and self.ib and self.ib.isConnected()
        )
        if not is_actually_connected:
            self.logger.warning(
                f"IBKR未连接 (self.connected={self.connected}, actual={is_actually_connected})"
            )
            raise IBKRConnectionError("未连接到 IBKR")

        # 舍入价格到合适的tick size
        rounded_limit_price = self._round_price_to_tick_size(limit_price)

        if abs(limit_price - rounded_limit_price) > 0.001:
            self.logger.debug(
                f"价格舍入: {symbol} 原价格 ${limit_price} -> 舍入后 ${rounded_limit_price}"
            )

        # 创建合约
        contract = self.create_stock_contract(symbol)
        await self.ib.qualifyContractsAsync(contract)

        # 检查是否为大订单
        if self._check_large_order(quantity):
            self.logger.warning(
                f"⚠️ 检测到大订单: {quantity} 股 (超过 {self.config.max_order_size} 股限制)"
            )

            # 优先使用算法订单
            if self.config.enable_algo_orders:
                try:
                    order = self._create_algo_order(
                        action, quantity, rounded_limit_price
                    )
                    trade = self.ib.placeOrder(contract, order)

                    self.logger.info(
                        f"✅ 已提交TWAP算法订单: {action} {quantity} 股 {symbol} @ ${rounded_limit_price}"
                    )
                    return trade
                except Exception as e:
                    self.logger.warning(f"算法订单提交失败: {e}，尝试订单分拆")

            # 如果算法订单不可用或失败，使用订单分拆
            if self.config.enable_order_splitting:
                return await self._place_split_orders(
                    contract, quantity, rounded_limit_price, action, symbol
                )
            self.logger.error(f"大订单处理被禁用，无法处理 {quantity} 股订单")
            raise ValueError(
                f"订单数量 {quantity} 超过限制 {self.config.max_order_size}，且大订单处理被禁用"
            )

        # 常规订单处理
        order = LimitOrder(action, quantity, rounded_limit_price)
        trade = self.ib.placeOrder(contract, order)

        self.logger.info(
            f"✅ 已提交限价订单: {action} {quantity} 股 {symbol} @ ${rounded_limit_price}"
        )
        return trade

    async def _place_split_orders(
        self,
        contract,
        total_quantity: int,
        limit_price: float,
        action: str,
        symbol: str,
    ) -> Optional[Order]:
        """执行订单分拆逻辑"""
        order_sizes = self._split_large_order(total_quantity)
        trades = []

        for i, order_size in enumerate(order_sizes):
            try:
                # 为每个分拆订单添加延迟
                if i > 0:
                    await asyncio.sleep(self.config.order_split_delay)

                order = LimitOrder(action, order_size, limit_price)
                trade = self.ib.placeOrder(contract, order)
                trades.append(trade)

                self.logger.info(
                    f"✅ 分拆订单 {i + 1}/{len(order_sizes)}: {action} {order_size} 股 {symbol} @ ${limit_price}"
                )

            except Exception as e:
                self.logger.error(f"分拆订单 {i + 1} 提交失败: {e}")
                continue

        if trades:
            self.logger.info(
                f"🎯 大订单分拆完成: {len(trades)}/{len(order_sizes)} 个订单成功提交"
            )
            return trades[0]  # 返回第一个订单作为主要引用
        self.logger.error("所有分拆订单都提交失败")
        return None

    @handle_errors(default_return={}, error_category=ErrorCategory.DATA)
    def get_account_summary(self) -> dict:
        """获取账户摘要信息"""
        # 检查实际连接状态而不是依赖self.connected标志
        is_actually_connected = (
            hasattr(self, "ib") and self.ib and self.ib.isConnected()
        )
        if not is_actually_connected:
            self.logger.warning(
                f"IBKR未连接，返回空账户信息 (self.connected={self.connected}, actual={is_actually_connected})"
            )
            return {}

        try:
            summary = self.ib.accountSummary()
            account_info = {}

            for item in summary:
                account_info[item.tag] = item.value

            return account_info
        except Exception as e:
            self.logger.error(f"Error getting account summary: {e}")
            return {}

    @async_handle_errors(default_return=None, error_category=ErrorCategory.DATA)
    async def get_contract_details(self, symbol: str) -> Optional[list]:
        """获取股票的详细合约信息"""
        if not self.connected:
            return None

        # 创建合约
        contract = Stock(symbol, "SMART", "USD")

        # 获取合约详情
        details = self.ib.reqContractDetails(contract)

        if details:
            return details
        self.logger.debug(f"No contract details found for {symbol}")
        return None

    @handle_errors(default_return=[], error_category=ErrorCategory.DATA)
    def get_portfolio(self) -> list[dict]:
        """获取当前投资组合仓位 (只显示非零仓位)"""
        if not self.connected:
            raise IBKRConnectionError("未连接到 IBKR")
            positions = self.ib.positions()
            portfolio = []

            for position in positions:
                if position.position != 0:  # 仅包含非零仓位
                    portfolio.append(
                        {
                            "symbol": position.contract.symbol,
                            "position": position.position,
                            "avgCost": position.avgCost,
                            "currency": position.contract.currency,
                            "exchange": position.contract.exchange,
                        }
                    )

            return portfolio

    async def get_positions(self) -> list:
        """从IBKR获取所有当前仓位"""
        if not self.connected:
            return []

        try:
            # 获取所有仓位 - 使用正确的ib_insync API
            positions = self.ib.positions()
            return positions

        except Exception as e:
            self.logger.error(f"Error getting positions: {e}")
            return []

    async def get_option_details(self, contract) -> Optional[dict]:
        """获取期权详细信息"""
        if not self.connected:
            return None

        try:
            # 首先确保合约已限定
            qualified_contracts = await self.ib.qualifyContractsAsync(contract)
            if not qualified_contracts:
                self.logger.error(f"无法限定期权合约: {contract}")
                return None

            qualified_contract = qualified_contracts[0]
            self.logger.debug(f"已限定期权合约: {qualified_contract}")

            # 请求期权市场数据 - 添加期权特定的数据类型
            # 100=Option Volume, 101=Option Open Interest, 104=Historical Volatility, 105=Average Option Volume, 106=Option Implied Volatility
            ticker = self.ib.reqMktData(
                qualified_contract, "100,101,104,105,106", False, False
            )

            # 等待更长时间让数据稳定
            await asyncio.sleep(4)

            # 强制更新ticker数据
            self.ib.sleep(0.1)

            if ticker:
                # 获取期权价格 - 使用多种价格源确保可靠性
                market_price = ticker.marketPrice()
                bid_price = (
                    ticker.bid
                    if ticker.bid and str(ticker.bid).lower() not in ["nan", "none"]
                    else 0.0
                )
                ask_price = (
                    ticker.ask
                    if ticker.ask and str(ticker.ask).lower() not in ["nan", "none"]
                    else 0.0
                )
                close_price = (
                    ticker.close
                    if ticker.close and str(ticker.close).lower() not in ["nan", "none"]
                    else 0.0
                )
                last_price = (
                    ticker.last
                    if ticker.last and str(ticker.last).lower() not in ["nan", "none"]
                    else 0.0
                )

                # 调试日志 - 显示所有可用的价格数据
                self.logger.info(
                    f"期权价格数据 {contract.symbol} {contract.right} ${contract.strike}: market={market_price}, bid={bid_price}, ask={ask_price}, close={close_price}, last={last_price}"
                )

                # 选择最可靠的价格
                option_price = 0.0
                if market_price and str(market_price).lower() not in [
                    "nan",
                    "none",
                    "0.0",
                ]:
                    option_price = float(market_price)
                elif last_price > 0:
                    option_price = float(last_price)
                elif bid_price > 0 and ask_price > 0:
                    option_price = (float(bid_price) + float(ask_price)) / 2  # 中间价
                elif close_price > 0:
                    option_price = float(close_price)
                elif bid_price > 0:
                    option_price = float(bid_price)
                elif ask_price > 0:
                    option_price = float(ask_price)

                if option_price > 0:
                    self.logger.info(
                        f"期权 {contract.symbol} {contract.right} ${contract.strike} 价格: ${option_price:.2f}"
                    )
                else:
                    self.logger.warning(
                        f"无法获取有效期权价格: {contract.symbol} {contract.right} ${contract.strike}"
                    )

                # 获取期权价格
                option_details = {
                    "price": option_price,
                    "bid": bid_price,
                    "ask": ask_price,
                    "close": close_price,
                    "last": last_price,
                    "volume": ticker.volume or 0,
                }

                # 获取标的股票价格
                if hasattr(contract, "symbol"):
                    underlying_price = await self.get_current_price(contract.symbol)
                    option_details["underlying_price"] = underlying_price or 0.0

                # 取消市场数据订阅
                self.ib.cancelMktData(qualified_contract)

                return option_details

        except Exception as e:
            self.logger.error(f"Error getting option details: {e}")
            return None

    async def get_option_price(
        self, symbol: str, option_type: str, strike: float, expiry: str
    ) -> Optional[dict]:
        """获取期权价格"""
        try:
            # 转换期权类型格式
            right = "C" if option_type.upper() == "CALL" else "P"

            # 创建期权合约（参数顺序：symbol, expiry, strike, right）
            contract = self.create_option_contract(symbol, expiry, strike, right)
            if not contract:
                return None

            # 获取期权详情
            return await self.get_option_details(contract)

        except Exception as e:
            self.logger.error(f"Error getting option price: {e}")
            return None

    def _round_price_to_tick_size(self, price: float) -> float:
        """
        将价格舍入到合适的tick size

        根据价格范围确定合适的最小变动单位：
        - $0.01 到 $1.00: $0.01 (1分)
        - $1.00 到 $100.00: $0.01 (1分)
        - $100.00 以上: $0.01 (1分)

        美股大部分股票的最小变动单位都是 $0.01
        """
        # 对于美股，大部分情况下最小变动单位是 $0.01
        return round(price, 2)

    def _check_large_order(self, quantity: int) -> bool:
        """检查是否为大订单（超过IBKR限制）"""
        return quantity > self.config.max_order_size

    def _split_large_order(self, quantity: int) -> list[int]:
        """将大订单分拆为多个小订单"""
        if quantity <= self.config.max_order_size:
            return [quantity]

        # 计算需要分拆的订单数量
        num_orders = (
            quantity + self.config.max_order_size - 1
        ) // self.config.max_order_size
        base_size = quantity // num_orders
        remainder = quantity % num_orders

        orders = []
        for i in range(num_orders):
            order_size = base_size + (1 if i < remainder else 0)
            orders.append(order_size)

        self.logger.info(
            f"💼 大订单分拆: {quantity} 股 -> {num_orders} 个订单: {orders}"
        )
        return orders

    def _create_algo_order(
        self, action: str, quantity: int, limit_price: float
    ) -> Order:
        """创建算法订单（TWAP）来处理大订单"""
        try:
            # 创建TWAP算法订单
            order = LimitOrder(action, quantity, limit_price)
            order.algoStrategy = "Twap"
            order.algoParams = [
                ("startTime", ""),  # 立即开始
                ("endTime", ""),  # 自动结束
                ("allowPastEndTime", True),
            ]
            order.tif = "DAY"  # 当日有效

            self.logger.info(
                f"🤖 创建TWAP算法订单: {action} {quantity} 股 @ ${limit_price}"
            )
            return order
        except Exception as e:
            self.logger.warning(f"创建算法订单失败，回退到普通订单: {e}")
            return LimitOrder(action, quantity, limit_price)

    async def place_stop_loss_order(
        self, symbol: str, quantity: int, stop_price: float, action: str = "SELL"
    ) -> Optional[Order]:
        """
        Place a stop loss order

        参数:
        -----------
        symbol: str
            股票
        quantity: int
            Number of shares
        stop_price: float
            Stop loss trigger price
        action: str
            Order action (usually "SELL" for stop loss)
        """
        if not self.connected:
            self.logger.error("未连接到 IBKR")
            return None

        try:
            # 舍入价格到合适的tick size
            rounded_stop_price = self._round_price_to_tick_size(stop_price)

            if abs(stop_price - rounded_stop_price) > 0.001:
                self.logger.debug(
                    f"价格舍入: {symbol} 原价格 ${stop_price} -> 舍入后 ${rounded_stop_price}"
                )

            # 创建合约
            contract = Stock(symbol, "SMART", "USD")
            await self.ib.qualifyContractsAsync(contract)

            # 创建止损订单
            order = StopOrder(action, quantity, rounded_stop_price)

            trade = self.ib.placeOrder(contract, order)

            self.logger.info(
                f"Placed {action} stop loss order for {quantity} shares of {symbol} at ${rounded_stop_price}"
            )
            return trade

        except Exception as e:
            self.logger.error(f"Error placing stop loss order for {symbol}: {e}")
            return None

    async def modify_stop_loss_order(self, symbol: str, new_stop_price: float) -> bool:
        """
        Modify existing stop loss order for a symbol

        参数:
        -----------
        symbol: str
            股票
        new_stop_price: float
            New stop loss trigger price
        """
        if not self.connected:
            self.logger.error("未连接到 IBKR")
            return False

        try:
            # 舍入价格到合适的tick size
            rounded_stop_price = self._round_price_to_tick_size(new_stop_price)

            if abs(new_stop_price - rounded_stop_price) > 0.001:
                self.logger.debug(
                    f"价格舍入: {symbol} 原价格 ${new_stop_price} -> 舍入后 ${rounded_stop_price}"
                )

            # 获取所有未结订单
            trades = self.ib.openTrades()

            # 查找该股票的止损订单
            for trade in trades:
                if (
                    trade.contract.symbol == symbol
                    and hasattr(trade.order, "auxPrice")
                    and trade.order.orderType == "STP"
                ):
                    # 修改订单
                    trade.order.auxPrice = rounded_stop_price
                    self.ib.placeOrder(trade.contract, trade.order)

                    self.logger.info(
                        f"Modified stop loss for {symbol} to ${rounded_stop_price}"
                    )
                    return True

            self.logger.warning(f"No stop loss order found for {symbol}")
            return False

        except Exception as e:
            self.logger.error(f"Error modifying stop loss for {symbol}: {e}")
            return False

    async def get_options_chain(
        self, symbol: str, expiry_date: str = None
    ) -> Optional[list]:
        """
        Get options chain for a symbol - improved version

        参数:
        -----------
        symbol: str
            股票
        expiry_date: str
            Expiry date in YYYY-MM-DD format (optional)
        """
        if not self.connected:
            self.logger.error("未连接到 IBKR")
            return None

        try:
            # 首先创建股票合约
            stock = Stock(symbol, "SMART", "USD")
            qualified_stocks = await self.ib.qualifyContractsAsync(stock)

            if not qualified_stocks:
                self.logger.error(f"Cannot qualify stock contract for {symbol}")
                return None

            stock = qualified_stocks[0]

            # 获取期权链参数
            chains = await self.ib.reqSecDefOptParamsAsync(
                stock.symbol, "", stock.secType, stock.conId
            )

            if not chains:
                self.logger.warning(f"No options chain found for {symbol}")
                return None

            # 获取第一个链条（通常是流动性最好的交易所）
            chain = chains[0]
            self.logger.info(
                f"找到{symbol}的期权链: {len(chain.expirations)}个到期日，{len(chain.strikes)}个行权价"
            )

            # 过滤到期日 - 获取下2-3个月的到期日
            expiries = sorted(chain.expirations)[:3]
            if expiry_date:
                target_expiry = expiry_date.replace("-", "")
                expiries = [exp for exp in expiries if exp == target_expiry]

            # 过滤行权价 - 获取当前价格附近的合理范围
            current_price = await self.get_current_price(symbol)
            if current_price:
                # 获取当前价格±20%范围内的行权价
                min_strike = current_price * 0.8
                max_strike = current_price * 1.2
                strikes = [s for s in chain.strikes if min_strike <= s <= max_strike]
            else:
                # 如果没有当前价格，使用行权价的中间范围
                strikes = sorted(chain.strikes)[
                    len(chain.strikes) // 4 : 3 * len(chain.strikes) // 4
                ]

            self.logger.info(f"使用{len(expiries)}个到期日和{len(strikes)}个行权价")

            # Create option contracts - 使用SMART交易所避免Error 200
            options = []
            for expiry in expiries:
                for strike in strikes[:20]:  # 限制行权价数量以避免过多请求
                    # 使用SMART交易所而不是特定交易所，提高成功率
                    call = Option(symbol, expiry, strike, "C", "SMART")
                    put = Option(symbol, expiry, strike, "P", "SMART")
                    options.extend([call, put])

            # 批量验证合约以避免速率限制
            qualified_options = []
            batch_size = 50

            for i in range(0, len(options), batch_size):
                batch = options[i : i + batch_size]
                try:
                    qualified_batch = await self.ib.qualifyContractsAsync(*batch)
                    # 过滤掉无效合约（避免Error 200）
                    valid_contracts = [c for c in qualified_batch if c.conId > 0]
                    qualified_options.extend(valid_contracts)

                    if len(valid_contracts) < len(qualified_batch):
                        invalid_count = len(qualified_batch) - len(valid_contracts)
                        self.logger.debug(
                            f"⚠️ Filtered out {invalid_count} invalid contracts in batch {i // batch_size + 1}"
                        )

                    await asyncio.sleep(0.5)  # 速率限制
                except Exception as e:
                    self.logger.warning(
                        f"Failed to qualify batch {i // batch_size + 1}: {e}"
                    )

            self.logger.info(f"成功确认{symbol}的{len(qualified_options)}个期权")
            return qualified_options

        except Exception as e:
            self.logger.error(f"Error getting options chain for {symbol}: {e}")
            return None

    async def place_options_order(
        self,
        symbol: str,
        expiry: str,
        strike: float,
        right: str,
        quantity: int,
        action: str = "BUY",
        order_type: str = "MKT",
        limit_price: float = None,
    ) -> Optional[Order]:
        """
        Place an options order

        参数:
        -----------
        symbol: str
            股票
        expiry: str
            YYYYMMDD格式的到期日期
        strike: float
            行权价格
        right: str
            "C"表示看涨期权，"P"表示看跌期权
        quantity: int
            合约数量
        action: str
            "BUY"表示买入，"SELL"表示卖出
        order_type: str
            "MKT"表示市价单，"LMT"表示限价单
        limit_price: float
            限价（限价单必需）
        """
        if not self.connected:
            self.logger.error("未连接到 IBKR")
            return None

        try:
            # 创建期权合约
            contract = Option(symbol, expiry, strike, right, "SMART")

            # 验证合约是否有效
            qualified_contracts = await self.ib.qualifyContractsAsync(contract)
            if not qualified_contracts:
                self.logger.error(f"❌ Invalid option contract: {contract}")
                return None

            contract = qualified_contracts[0]

            # 创建订单
            if order_type == "MKT":
                order = MarketOrder(action, quantity)
            elif order_type == "LMT":
                if limit_price is None:
                    raise ValueError("限价单需要指定限价")
                order = LimitOrder(action, quantity, limit_price)
            else:
                raise ValueError(f"不支持的订单类型: {order_type}")

            trade = self.ib.placeOrder(contract, order)

            option_desc = f"{right} {symbol} {expiry} {strike}"
            self.logger.info(
                f"Placed {action} {order_type} order for {quantity} {option_desc} contracts"
            )
            return trade

        except Exception as e:
            self.logger.error(f"Error placing options order: {e}")
            return None

    async def get_options_market_data(self, contracts: list) -> dict:
        """
        Get market data for options contracts

        参数:
        -----------
        contracts: list
            List of option contracts
        """
        if not self.connected:
            self.logger.error("未连接到 IBKR")
            return {}

        try:
            # 为所有合约请求市场数据
            tickers = []
            for contract in contracts[:20]:  # 限制数量以避免速率限制
                ticker = self.ib.reqMktData(contract)
                tickers.append(ticker)

            # 等待数据
            await asyncio.sleep(2)

            market_data = {}
            for ticker in tickers:
                if ticker.contract:
                    # 尝试获取价格数据，优先级：last > close > mid > 模拟价格
                    price = None
                    if ticker.last and ticker.last > 0:
                        price = ticker.last
                    elif ticker.close and ticker.close > 0:
                        price = ticker.close
                    elif (
                        ticker.bid and ticker.ask and ticker.bid > 0 and ticker.ask > 0
                    ):
                        price = (ticker.bid + ticker.ask) / 2
                    else:
                        # 如果没有市场数据，使用基于内在价值的模拟价格
                        price = self._estimate_option_price(ticker.contract)

                    if price and price > 0:
                        key = f"{ticker.contract.symbol}_{ticker.contract.lastTradeDateOrContractMonth}_{ticker.contract.strike}_{ticker.contract.right}"
                        market_data[key] = {
                            "symbol": ticker.contract.symbol,
                            "expiry": ticker.contract.lastTradeDateOrContractMonth,
                            "strike": ticker.contract.strike,
                            "right": ticker.contract.right,
                            "last_price": price,
                            "bid": ticker.bid
                            if ticker.bid and ticker.bid > 0
                            else price * 0.95,
                            "ask": ticker.ask
                            if ticker.ask and ticker.ask > 0
                            else price * 1.05,
                            "volume": ticker.volume
                            if ticker.volume
                            else 100,  # 模拟交易量
                            "open_interest": getattr(
                                ticker, "openInterest", 500
                            ),  # 模拟持仓量
                        }

            self.logger.info(f"检索到{len(market_data)}个期权的市场数据")
            return market_data

        except Exception as e:
            self.logger.error(f"Error getting options market data: {e}")
            return {}

    def _estimate_option_price(self, contract) -> float:
        """估算期权价格（当没有市场数据时）"""
        try:
            # 基于内在价值和时间价值的简单估算
            strike = contract.strike

            # 假设标的股票价格（实际应该获取真实价格）
            underlying_price = 100.0  # 简化假设

            # 计算内在价值
            if contract.right == "C":  # 看涨期权
                intrinsic_value = max(0, underlying_price - strike)
            else:  # 看跌期权
                intrinsic_value = max(0, strike - underlying_price)

            # 添加时间价值（简化估算）
            time_value = 2.0  # 固定时间价值

            estimated_price = intrinsic_value + time_value

            # 确保最小价格
            return max(0.5, estimated_price)

        except Exception as e:
            self.logger.warning(f"Error estimating option price: {e}")
            return 2.0  # 默认价格

    async def get_current_price(self, symbol: str) -> Optional[float]:
        """
        获取当前股票价格 - 处理市场关闭情况

        参数:
        -----------
        symbol: str
            股票
        """
        if not self.connected:
            self.logger.error("未连接到 IBKR")
            return None

        try:
            contract = self.create_stock_contract(symbol)
            qualified_contracts = await self.ib.qualifyContractsAsync(contract)

            if not qualified_contracts:
                self.logger.error(f"Cannot qualify contract for {symbol}")
                return None

            contract = qualified_contracts[0]

            # 首先尝试获取实时数据
            ticker = self.ib.reqMktData(contract)
            await asyncio.sleep(3)  # 等待更长时间获取数据

            price = None

            # 价格数据的优先级顺序 - 安全获取属性值
            def safe_get_price(attr_value):
                """安全获取价格属性值"""
                if attr_value is None or attr_value == -1 or attr_value == 0:
                    return None
                try:
                    # 如果是方法，调用它；如果是属性，直接返回
                    if callable(attr_value):
                        return float(attr_value())
                    return float(attr_value)
                except (TypeError, ValueError):
                    return None

            last_price = safe_get_price(ticker.last)
            close_price = safe_get_price(ticker.close)
            bid_price = safe_get_price(ticker.bid)
            ask_price = safe_get_price(ticker.ask)
            market_price = safe_get_price(ticker.marketPrice)

            if last_price and last_price > 0:
                price = last_price
                price_type = "last"
            elif close_price and close_price > 0:
                price = close_price
                price_type = "close"
            elif bid_price and ask_price and bid_price > 0 and ask_price > 0:
                price = (bid_price + ask_price) / 2
                price_type = "mid"
            elif market_price and market_price > 0:
                price = market_price
                price_type = "market"

            # 如果没有实时数据，尝试历史数据
            if not price:
                self.logger.info(f"{symbol}没有实时数据，尝试历史数据")
                try:
                    end_date = datetime.now() - timedelta(days=5)  # 过去5天

                    bars = await self.ib.reqHistoricalDataAsync(
                        contract,
                        endDateTime=end_date,
                        durationStr="5 D",
                        barSizeSetting="1 day",
                        whatToShow="TRADES",
                        useRTH=True,
                    )

                    if bars:
                        price = bars[-1].close  # 最后收盘价
                        price_type = "historical_close"

                except Exception as hist_e:
                    self.logger.warning(f"Historical data request failed: {hist_e}")

            # 清理
            try:
                self.ib.cancelMktData(contract)
            except Exception as e:
                self.logger.debug(f"Failed to cancel market data for {symbol}: {e}")

            if price and price > 0:
                self.logger.info(f"{symbol}的价格: ${price:.2f} ({price_type})")
                return price
            self.logger.warning(f"No valid price data available for {symbol}")
            return None

        except Exception as e:
            self.logger.error(f"Error getting current price for {symbol}: {e}")
            return None


# 全局客户端管理器实例
_client_manager = IBKRClientManager()


def get_ibkr_client(purpose: str = "default", config=None) -> IBKRClient:
    """
    获取IBKR客户端实例的便利函数

    参数:
        purpose: 客户端用途 ('default', 'trading', 'data', 'options', 'updater')
        config: IBKR配置

    返回:
        IBKRClient实例
    """
    return _client_manager.get_client(purpose, config)


def disconnect_all_ibkr_clients(exclude_purposes: list[str] = None):
    """
    断开所有IBKR客户端连接的便利函数

    参数:
        exclude_purposes: 要排除的客户端用途列表，这些连接将被保留
    """
    _client_manager.disconnect_all(exclude_purposes)


def selective_disconnect_ibkr_clients(purposes: list[str]):
    """
    选择性断开指定用途的IBKR客户端连接的便利函数

    参数:
        purposes: 要断开的客户端用途列表
    """
    _client_manager.selective_disconnect(purposes)

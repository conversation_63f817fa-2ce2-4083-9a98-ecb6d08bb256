#!/usr/bin/env python3
"""
期权交易性能监控系统

实时监控期权交易策略的表现，提供全面的风险预警和性能分析。
包含仓位跟踪、盈亏分析、Greeks监控、风险指标计算等功能。
支持多策略监控和自动化风险管理。
"""

import asyncio
from dataclasses import asdict, dataclass
from datetime import datetime

import matplotlib.pyplot as plt
import numpy as np

# 导入统一配置
# 导入统一异常处理
from core.exceptions import ErrorCategory, async_handle_errors, handle_errors

# 使用统一的日志配置
from core.utils import setup_unified_logging

logger = setup_unified_logging(__name__)


@dataclass
class OptionsPosition:
    """期权仓位信息"""

    symbol: str
    option_type: str  # 看涨/看跌
    strike: float
    expiry: str
    quantity: int
    entry_price: float
    current_price: float
    entry_time: datetime
    underlying_price: float
    ibkr_contract: object = None  # 保存原始IBKR合约


@dataclass
class PerformanceMetrics:
    """性能指标"""

    total_pnl: float
    realized_pnl: float
    unrealized_pnl: float
    win_rate: float
    avg_win: float
    avg_loss: float
    max_drawdown: float
    sharpe_ratio: float
    total_trades: int
    winning_trades: int
    losing_trades: int
    current_positions: int


class OptionsPerformanceMonitor:
    """期权性能监控器"""

    def __init__(self, config=None):
        self.config = config
        self.positions: dict[str, OptionsPosition] = {}
        self.closed_trades: list[dict] = []
        self.performance_history: list[dict] = []
        self.risk_alerts: list[dict] = []

        # 监控参数
        self.max_single_position_loss = 0.5  # 单仓位最大亏损50%
        self.max_portfolio_loss = 0.15  # 组合最大亏损15%

    @async_handle_errors(default_return=False, error_category=ErrorCategory.TRADING)
    async def add_position(self, position: OptionsPosition) -> bool:
        """添加新仓位"""
        position_id = f"{position.symbol}_{position.option_type}_{position.strike}_{position.expiry}"
        self.positions[position_id] = position

        logger.info(f"✅ 添加期权仓位: {position_id}")
        logger.info(
            f"   入场价: ${position.entry_price:.2f}, 数量: {position.quantity}"
        )

        # 检查风险
        await self.check_position_risk(position_id)

        return True

    @async_handle_errors(default_return=False, error_category=ErrorCategory.DATA)
    async def update_position_prices(self, price_updates: dict[str, dict]) -> bool:
        """更新仓位价格"""
        for position_id, position in self.positions.items():
            symbol = position.symbol

            if symbol in price_updates:
                # 更新标的价格
                position.underlying_price = price_updates[symbol].get(
                    "underlying_price", position.underlying_price
                )

                # 更新期权价格
                option_key = (
                    f"{position.option_type}_{position.strike}_{position.expiry}"
                )
                if option_key in price_updates[symbol]:
                    option_data = price_updates[symbol][option_key]
                    position.current_price = option_data.get(
                        "price", position.current_price
                    )

        # 更新性能指标
        await self.update_performance_metrics()

        # 检查风险警报
        await self.check_risk_alerts()

        return True

    @async_handle_errors(default_return=False, error_category=ErrorCategory.TRADING)
    async def close_position(
        self, position_id: str, exit_price: float, exit_time: datetime = None
    ) -> bool:
        """平仓"""
        if position_id not in self.positions:
            logger.warning(f"未找到仓位 {position_id}")
            return False

        position = self.positions[position_id]
        exit_time = exit_time or datetime.now()

        # 计算盈亏
        # 期权P&L直接计算：(出场价格 - 入场价格) × 数量
        pnl = (exit_price - position.entry_price) * position.quantity
        pnl_pct = (exit_price - position.entry_price) / position.entry_price

        # 记录交易
        trade_record = {
            "position_id": position_id,
            "symbol": position.symbol,
            "option_type": position.option_type,
            "strike": position.strike,
            "expiry": position.expiry,
            "quantity": position.quantity,
            "entry_price": position.entry_price,
            "exit_price": exit_price,
            "entry_time": position.entry_time,
            "exit_time": exit_time,
            "holding_days": (exit_time - position.entry_time).days,
            "pnl": pnl,
            "pnl_pct": pnl_pct,
            "underlying_entry": position.underlying_price,
            "underlying_exit": position.underlying_price,  # 需要实时更新
        }

        self.closed_trades.append(trade_record)
        del self.positions[position_id]

        logger.info(f"✅ 关闭仓位: {position_id}")
        logger.info(f"   盈亏: ${pnl:.2f} ({pnl_pct * 100:.1f}%)")

        return True

    @async_handle_errors(default_return=None, error_category=ErrorCategory.DATA)
    async def update_performance_metrics(self) -> None:
        """更新性能指标"""
        # 计算未实现盈亏
        unrealized_pnl = 0
        for position in self.positions.values():
            # 期权P&L直接计算：(当前价格 - 入场价格) × 数量
            position_pnl = (
                position.current_price - position.entry_price
            ) * position.quantity
            unrealized_pnl += position_pnl

            # 计算已实现盈亏
            realized_pnl = sum(trade["pnl"] for trade in self.closed_trades)

            # 计算胜率
            if self.closed_trades:
                winning_trades = len([t for t in self.closed_trades if t["pnl"] > 0])
                win_rate = winning_trades / len(self.closed_trades)
                avg_win = (
                    np.mean([t["pnl"] for t in self.closed_trades if t["pnl"] > 0])
                    if winning_trades > 0
                    else 0
                )
                avg_loss = (
                    np.mean([t["pnl"] for t in self.closed_trades if t["pnl"] < 0])
                    if len(self.closed_trades) - winning_trades > 0
                    else 0
                )
            else:
                win_rate = 0
                avg_win = 0
                avg_loss = 0
                winning_trades = 0

            # 计算最大回撤
            pnl_series = [t["pnl"] for t in self.closed_trades]
            if pnl_series:
                cumulative_pnl = np.cumsum(pnl_series)
                running_max = np.maximum.accumulate(cumulative_pnl)
                drawdown = cumulative_pnl - running_max
                max_drawdown = np.min(drawdown) if len(drawdown) > 0 else 0
            else:
                max_drawdown = 0

            # 计算夏普比率（简化版）
            if pnl_series and len(pnl_series) > 1:
                returns = np.array(pnl_series)
                sharpe_ratio = (
                    np.mean(returns) / np.std(returns) if np.std(returns) > 0 else 0
                )
            else:
                sharpe_ratio = 0

            # 创建性能指标
            metrics = PerformanceMetrics(
                total_pnl=realized_pnl + unrealized_pnl,
                realized_pnl=realized_pnl,
                unrealized_pnl=unrealized_pnl,
                win_rate=win_rate,
                avg_win=avg_win,
                avg_loss=avg_loss,
                max_drawdown=max_drawdown,
                sharpe_ratio=sharpe_ratio,
                total_trades=len(self.closed_trades),
                winning_trades=winning_trades,
                losing_trades=len(self.closed_trades) - winning_trades,
                current_positions=len(self.positions),
            )

            # 记录历史
            self.performance_history.append(
                {"timestamp": datetime.now(), "metrics": asdict(metrics)}
            )

    async def check_position_risk(self, position_id: str) -> None:
        """检查单个仓位风险"""
        try:
            position = self.positions[position_id]

            # 检查单仓位亏损
            current_pnl_pct = (
                position.current_price - position.entry_price
            ) / position.entry_price
            if current_pnl_pct < -self.max_single_position_loss:
                alert = {
                    "type": "SINGLE_POSITION_LOSS",
                    "position_id": position_id,
                    "current_loss": current_pnl_pct,
                    "threshold": -self.max_single_position_loss,
                    "timestamp": datetime.now(),
                    "message": f"仓位 {position_id} 亏损超过 {self.max_single_position_loss * 100:.0f}%",
                }
                self.risk_alerts.append(alert)
                logger.warning(f"⚠️ 风险警告: {alert['message']}")

        except Exception as e:
            logger.error(f"检查仓位风险错误: {e}")

    async def check_risk_alerts(self) -> None:
        """检查风险警报"""
        try:
            # 检查组合总亏损
            # 期权P&L直接计算：(当前价格 - 入场价格) × 数量
            total_unrealized_pnl = sum(
                (pos.current_price - pos.entry_price) * pos.quantity
                for pos in self.positions.values()
            )

            # 假设初始资金（需要从配置获取）
            initial_capital = 100000  # $100,000
            portfolio_loss_pct = total_unrealized_pnl / initial_capital

            if portfolio_loss_pct < -self.max_portfolio_loss:
                alert = {
                    "type": "PORTFOLIO_LOSS",
                    "current_loss_pct": portfolio_loss_pct,
                    "threshold": -self.max_portfolio_loss,
                    "timestamp": datetime.now(),
                    "message": f"Portfolio loss {portfolio_loss_pct * 100:.1f}% exceeds limit {self.max_portfolio_loss * 100:.0f}%",
                }
                self.risk_alerts.append(alert)
                logger.warning(f"⚠️ 风险警告: {alert['message']}")

        except Exception as e:
            logger.error(f"检查风险警报错误: {e}")

    def get_current_performance(self) -> dict:
        """获取当前性能报告"""
        if not self.performance_history:
            return {"error": "无性能数据可用"}

        latest_metrics = self.performance_history[-1]["metrics"]

        # 添加仓位详情
        position_summary = []
        for pos_id, pos in self.positions.items():
            # 期权P&L直接计算：(当前价格 - 入场价格) × 数量
            pnl = (pos.current_price - pos.entry_price) * pos.quantity
            pnl_pct = (pos.current_price - pos.entry_price) / pos.entry_price

            position_summary.append(
                {
                    "position_id": pos_id,
                    "symbol": pos.symbol,
                    "type": pos.option_type,
                    "strike": pos.strike,
                    "expiry": pos.expiry,
                    "quantity": pos.quantity,
                    "entry_price": pos.entry_price,
                    "current_price": pos.current_price,
                    "pnl": pnl,
                    "pnl_pct": pnl_pct,
                }
            )

        return {
            "timestamp": datetime.now(),
            "performance_metrics": latest_metrics,
            "current_positions": position_summary,
            "recent_alerts": self.risk_alerts[-5:] if self.risk_alerts else [],
        }

    async def get_current_positions_from_ibkr(
        self, ibkr_config
    ) -> list[OptionsPosition]:
        """从IBKR获取当前期权仓位"""
        try:
            from core.broker import IBKRClient

            client = IBKRClient(ibkr_config)
            if not await client.connect():
                logger.error("连接到 IBKR 进行仓位监控失败")
                return []

            try:
                # 获取所有仓位
                positions = await client.get_positions()
                options_positions = []

                for pos in positions:
                    # 检查是否为期权仓位
                    if (
                        hasattr(pos.contract, "secType")
                        and pos.contract.secType == "OPT"
                    ):
                        # 获取期权详情
                        option_details = await client.get_option_details(pos.contract)

                        if option_details:
                            # 创建OptionsPosition对象
                            # IBKR的avgCost已经包含100倍乘数，需要除以100得到实际期权价格
                            entry_price = (
                                float(pos.avgCost) / 100 if pos.avgCost else 0.0
                            )

                            options_position = OptionsPosition(
                                symbol=pos.contract.symbol,
                                option_type="CALL"
                                if pos.contract.right == "C"
                                else "PUT",
                                strike=float(pos.contract.strike),
                                expiry=pos.contract.lastTradeDateOrContractMonth,
                                quantity=int(pos.position),
                                entry_price=entry_price,  # 实际期权价格
                                current_price=float(
                                    option_details.get("price", 0)
                                ),  # 实际期权价格
                                entry_time=datetime.now(),  # 需要从交易记录获取
                                underlying_price=float(
                                    option_details.get("underlying_price", 0)
                                ),
                                ibkr_contract=pos.contract,  # 保存原始合约
                            )

                            options_positions.append(options_position)

                            # 添加到监控系统
                            position_id = f"{options_position.symbol}_{options_position.option_type}_{options_position.strike}_{options_position.expiry}"
                            self.positions[position_id] = options_position

                logger.info(f"📊 从IBKR获取到 {len(options_positions)} 个期权仓位")
                return options_positions

            finally:
                client.disconnect()

        except Exception as e:
            logger.error(f"从IBKR获取仓位错误: {e}")
            return []

    async def update_all_positions_from_ibkr(self, ibkr_config) -> bool:
        """从IBKR更新所有仓位的实时价格"""
        try:
            from core.broker import IBKRClient

            if not self.positions:
                logger.info("没有仓位需要更新")
                return True

            client = IBKRClient(ibkr_config)
            if not await client.connect():
                logger.error("连接到 IBKR 进行价格更新失败")
                return False

            try:
                updated_count = 0

                for position_id, position in self.positions.items():
                    try:
                        # 使用保存的原始合约获取期权实时价格
                        if position.ibkr_contract:
                            option_data = await client.get_option_details(
                                position.ibkr_contract
                            )
                        else:
                            # 如果没有保存的合约，则重新创建（兼容性）
                            option_data = await client.get_option_price(
                                position.symbol,
                                position.option_type,
                                position.strike,
                                position.expiry,
                            )

                        if option_data:
                            # 更新价格和希腊字母
                            position.current_price = float(
                                option_data.get("price", position.current_price)
                            )
                            position.underlying_price = float(
                                option_data.get(
                                    "underlying_price", position.underlying_price
                                )
                            )

                            updated_count += 1

                    except Exception as e:
                        logger.warning(f"更新仓位{position_id}失败: {e}")
                        continue

                logger.info(
                    f"✅ 更新了 {updated_count}/{len(self.positions)} 个仓位价格"
                )

                # 更新性能指标
                await self.update_performance_metrics()

                return True

            finally:
                client.disconnect()

        except Exception as e:
            logger.error(f"从 IBKR 更新仓位错误: {e}")
            return False

    async def check_exit_conditions(self) -> list[dict]:
        """检查所有仓位的出场条件 - 使用配置化的出场策略"""
        exit_signals = []

        try:
            from config import DEFAULT_EXIT_CONDITIONS, ExitReason

            for position_id, position in self.positions.items():
                # 计算当前盈亏
                current_pnl_pct = (
                    position.current_price - position.entry_price
                ) / position.entry_price
                days_to_expiry = self.days_to_expiry(position.expiry)

                # 检查所有配置的出场条件
                triggered_conditions = []

                for condition in DEFAULT_EXIT_CONDITIONS:
                    should_exit = False

                    # 检查不同类型的出场条件
                    if (
                        (
                            condition.reason == ExitReason.TAKE_PROFIT_25PCT
                            and current_pnl_pct >= 0.25
                        )
                        or (
                            condition.reason == ExitReason.TAKE_PROFIT_50PCT
                            and current_pnl_pct >= 0.50
                        )
                        or (
                            condition.reason == ExitReason.TAKE_PROFIT_75PCT
                            and current_pnl_pct >= 0.75
                        )
                        or (
                            condition.reason == ExitReason.STOP_LOSS_25PCT
                            and current_pnl_pct <= -0.25
                        )
                        or (
                            condition.reason == ExitReason.STOP_LOSS_50PCT
                            and current_pnl_pct <= -0.50
                        )
                        or (
                            condition.reason == ExitReason.STOP_LOSS_75PCT
                            and current_pnl_pct <= -0.75
                        )
                        or (
                            condition.reason == ExitReason.TIME_DECAY_EXIT
                            and days_to_expiry <= condition.threshold
                        )
                    ):
                        should_exit = True
                    if should_exit:
                        triggered_conditions.append(condition)

                # 如果有触发的条件，选择最高优先级的
                if triggered_conditions:
                    # 按紧急程度排序，选择最紧急的
                    urgency_order = {"CRITICAL": 4, "HIGH": 3, "MEDIUM": 2, "LOW": 1}
                    primary_condition = max(
                        triggered_conditions,
                        key=lambda c: urgency_order.get(c.urgency.value, 0),
                    )

                    exit_signal = {
                        "position_id": position_id,
                        "position": position,
                        "exit_reason": primary_condition.reason.value,
                        "exit_description": primary_condition.description,
                        "current_pnl_pct": current_pnl_pct,
                        "recommended_exit_price": position.current_price,
                        "urgency": primary_condition.urgency.value,
                        "auto_execute": primary_condition.auto_execute,
                        "days_to_expiry": days_to_expiry,
                        "triggered_conditions": [
                            c.reason.value for c in triggered_conditions
                        ],
                    }
                    exit_signals.append(exit_signal)

                    logger.info(f"🚨 出场信号: {position_id}")
                    logger.info(f"   原因: {primary_condition.description}")
                    logger.info(f"   盈亏: {current_pnl_pct * 100:.1f}%")
                    logger.info(f"   紧急程度: {primary_condition.urgency.value}")
                    logger.info(
                        f"   自动执行: {'是' if primary_condition.auto_execute else '否'}"
                    )
                    if len(triggered_conditions) > 1:
                        logger.info(
                            f"   其他触发条件: {[c.description for c in triggered_conditions[1:]]}"
                        )

            return exit_signals

        except Exception as e:
            logger.error(f"检查出场条件错误: {e}")
            return []

    @handle_errors(default_return=30, error_category=ErrorCategory.DATA)
    def days_to_expiry(self, expiry_str: str) -> int:
        """计算距离到期的天数"""
        # 解析到期日期 (格式: YYYYMMDD 或 YYYY-MM-DD)
        if "-" in expiry_str:
            expiry_date = datetime.strptime(expiry_str, "%Y-%m-%d").date()
        else:
            expiry_date = datetime.strptime(expiry_str, "%Y%m%d").date()

        today = datetime.now().date()
        return (expiry_date - today).days

    def calculate_exit_urgency(self, exit_reason: str, pnl_pct: float) -> str:
        """计算出场紧急程度"""
        if exit_reason == "STOP_LOSS_50PCT" or exit_reason == "TIME_DECAY_EXIT":
            return "HIGH"
        if exit_reason == "TAKE_PROFIT_50PCT":
            return "MEDIUM"
        if exit_reason == "IV_COLLAPSE_EXIT":
            return "LOW"
        return "LOW"

    async def execute_exit_trade(self, exit_signal: dict, ibkr_config) -> bool:
        """执行出场交易"""
        try:
            from core.broker import IBKRClient

            position = exit_signal["position"]
            position_id = exit_signal["position_id"]

            logger.info(f"🚀 执行出场交易: {position_id}")
            logger.info(f"   原因: {exit_signal['exit_reason']}")
            logger.info(f"   数量: {position.quantity}")
            logger.info(f"   价格: ${exit_signal['recommended_exit_price']:.2f}")

            client = IBKRClient(ibkr_config)
            if not await client.connect():
                logger.error("连接到 IBKR 进行出场交易失败")
                return False

            try:
                # 创建期权合约
                contract = await client.create_option_contract(
                    symbol=position.symbol,
                    option_type=position.option_type,
                    strike=position.strike,
                    expiry=position.expiry,
                )

                if not contract:
                    logger.error(f"为{position_id}创建合约失败")
                    return False

                # 创建卖出订单 (平仓)
                order = await client.create_market_order(
                    action="SELL",  # 卖出平仓
                    quantity=abs(position.quantity),
                    order_type="MKT",  # 市价单确保成交
                )

                # 提交订单
                trade = await client.place_order(contract, order)

                if trade:
                    logger.info(f"✅ 出场订单已提交: 订单号 {trade.order.orderId}")

                    # 等待订单成交
                    await asyncio.sleep(2)  # 等待成交确认

                    # 记录出场交易
                    await self.close_position(
                        position_id=position_id,
                        exit_price=exit_signal["recommended_exit_price"],
                        exit_time=datetime.now(),
                    )

                    return True
                logger.error(f"为{position_id}下出场订单失败")
                return False

            finally:
                client.disconnect()

        except Exception as e:
            logger.error(f"执行{exit_signal['position_id']}出场交易时出错: {e}")
            return False

    def generate_performance_chart(self, save_path: str = None) -> str:
        """生成性能图表"""
        try:
            if not self.closed_trades:
                logger.warning("没有已关闭的交易可绘制图表")
                return None

            # 创建图表
            fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 10))

            # 1. 累积盈亏曲线
            pnl_series = [trade["pnl"] for trade in self.closed_trades]
            cumulative_pnl = np.cumsum(pnl_series)
            ax1.plot(cumulative_pnl, linewidth=2, color="blue")
            ax1.set_title("Cumulative P&L")
            ax1.set_xlabel("Trade Number")
            ax1.set_ylabel("Cumulative P&L ($)")
            ax1.grid(True, alpha=0.3)

            # 2. 盈亏分布
            ax2.hist(pnl_series, bins=20, alpha=0.7, color="green", edgecolor="black")
            ax2.set_title("P&L Distribution")
            ax2.set_xlabel("P&L ($)")
            ax2.set_ylabel("Frequency")
            ax2.axvline(x=0, color="red", linestyle="--", alpha=0.7)

            # 3. 胜率统计
            wins = len([p for p in pnl_series if p > 0])
            losses = len([p for p in pnl_series if p < 0])
            ax3.pie(
                [wins, losses],
                labels=["Wins", "Losses"],
                autopct="%1.1f%%",
                colors=["green", "red"],
                startangle=90,
            )
            ax3.set_title("Win/Loss Ratio")

            # 4. 持仓时间分析
            holding_days = [trade["holding_days"] for trade in self.closed_trades]
            ax4.hist(
                holding_days, bins=15, alpha=0.7, color="orange", edgecolor="black"
            )
            ax4.set_title("Holding Period Distribution")
            ax4.set_xlabel("Days")
            ax4.set_ylabel("Frequency")

            plt.tight_layout()

            # 保存图表
            if save_path:
                plt.savefig(save_path, dpi=300, bbox_inches="tight")
                logger.info(f"绩效图表已保存到 {save_path}")

            return save_path

        except Exception as e:
            logger.error(f"生成绩效图表错误: {e}")
            return None
